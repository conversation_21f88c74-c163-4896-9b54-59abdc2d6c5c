import React from 'react';

// 性能监控工具
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number> = new Map();
  private observers: PerformanceObserver[] = [];

  private constructor() {
    this.initializeObservers();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private initializeObservers(): void {
    // 监控 Core Web Vitals
    if ('PerformanceObserver' in window) {
      // LCP (Largest Contentful Paint)
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          if (lastEntry) {
            this.metrics.set('LCP', lastEntry.startTime);
            this.logMetric('LCP', lastEntry.startTime);
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP observer not supported');
      }

      // FID (First Input Delay)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.metrics.set('FID', entry.processingStart - entry.startTime);
            this.logMetric('FID', entry.processingStart - entry.startTime);
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (e) {
        console.warn('FID observer not supported');
      }

      // CLS (Cumulative Layout Shift)
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
              this.metrics.set('CLS', clsValue);
            }
          });
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (e) {
        console.warn('CLS observer not supported');
      }

      // 监控资源加载
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.name.includes('.mp3') || entry.name.includes('.wav')) {
              this.logAudioLoadTime(entry.name, entry.duration);
            }
          });
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);
      } catch (e) {
        console.warn('Resource observer not supported');
      }
    }
  }

  // 记录自定义指标
  markStart(name: string): void {
    performance.mark(`${name}-start`);
  }

  markEnd(name: string): number {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    
    const measure = performance.getEntriesByName(name, 'measure')[0];
    const duration = measure ? measure.duration : 0;
    
    this.metrics.set(name, duration);
    this.logMetric(name, duration);
    
    return duration;
  }

  // 记录音频加载时间
  private logAudioLoadTime(url: string, duration: number): void {
    const fileName = url.split('/').pop() || 'unknown';
    console.log(`音频加载时间 - ${fileName}: ${duration.toFixed(2)}ms`);
    
    if (duration > 3000) {
      console.warn(`音频加载较慢 - ${fileName}: ${duration.toFixed(2)}ms`);
    }
  }

  // 记录指标
  private logMetric(name: string, value: number): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`性能指标 - ${name}: ${value.toFixed(2)}ms`);
      
      // 性能警告阈值
      const thresholds = {
        LCP: 2500, // 2.5秒
        FID: 100,  // 100毫秒
        CLS: 0.1   // 0.1
      };
      
      if (name in thresholds && value > thresholds[name as keyof typeof thresholds]) {
        console.warn(`性能警告 - ${name} 超过推荐阈值: ${value.toFixed(2)} > ${thresholds[name as keyof typeof thresholds]}`);
      }
    }
  }

  // 获取所有指标
  getMetrics(): Record<string, number> {
    return Object.fromEntries(this.metrics);
  }

  // 获取特定指标
  getMetric(name: string): number | undefined {
    return this.metrics.get(name);
  }

  // 清理观察者
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }

  // 生成性能报告
  generateReport(): string {
    const metrics = this.getMetrics();
    const report = [
      '=== 性能报告 ===',
      `LCP (最大内容绘制): ${metrics.LCP?.toFixed(2) || 'N/A'}ms`,
      `FID (首次输入延迟): ${metrics.FID?.toFixed(2) || 'N/A'}ms`,
      `CLS (累积布局偏移): ${metrics.CLS?.toFixed(3) || 'N/A'}`,
      '',
      '自定义指标:',
      ...Object.entries(metrics)
        .filter(([key]) => !['LCP', 'FID', 'CLS'].includes(key))
        .map(([key, value]) => `${key}: ${value.toFixed(2)}ms`)
    ];
    
    return report.join('\n');
  }
}

// 音频性能监控
export const monitorAudioPerformance = (audioElement: HTMLAudioElement, lessonTitle: string) => {
  const monitor = PerformanceMonitor.getInstance();
  const startTime = performance.now();
  
  const handleLoadStart = () => {
    monitor.markStart(`audio-load-${lessonTitle}`);
  };
  
  const handleCanPlay = () => {
    const loadTime = monitor.markEnd(`audio-load-${lessonTitle}`);
    console.log(`音频加载完成 - ${lessonTitle}: ${loadTime.toFixed(2)}ms`);
  };
  
  const handleError = () => {
    const errorTime = performance.now() - startTime;
    console.error(`音频加载失败 - ${lessonTitle}: ${errorTime.toFixed(2)}ms`);
  };
  
  audioElement.addEventListener('loadstart', handleLoadStart);
  audioElement.addEventListener('canplay', handleCanPlay);
  audioElement.addEventListener('error', handleError);
  
  // 返回清理函数
  return () => {
    audioElement.removeEventListener('loadstart', handleLoadStart);
    audioElement.removeEventListener('canplay', handleCanPlay);
    audioElement.removeEventListener('error', handleError);
  };
};

// 组件渲染性能监控
export const withPerformanceMonitoring = <P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) => {
  const WrappedComponent = (props: P) => {
    const monitor = PerformanceMonitor.getInstance();
    
    React.useEffect(() => {
      monitor.markStart(`component-render-${componentName}`);
      
      return () => {
        monitor.markEnd(`component-render-${componentName}`);
      };
    }, []);
    
    return React.createElement(Component, props);
  };
  
  WrappedComponent.displayName = `withPerformanceMonitoring(${componentName})`;
  
  return WrappedComponent;
};

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance();