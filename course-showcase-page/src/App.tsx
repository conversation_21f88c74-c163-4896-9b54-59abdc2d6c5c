import { ThemeProvider } from './theme'
import { CourseShowcasePage } from './components'
import { AudioPlayerLazy } from './components/AudioPlayerLazy'
import { ErrorBoundary } from './components/ErrorBoundary'
import { SimpleAudioTest } from './components/SimpleAudioTest'
import { SimpleAudioPlayer } from './components/SimpleAudioPlayer'
import { DirectAudioTest } from './components/DirectAudioTest'
import { SimpleAudioPlayerTest } from './components/SimpleAudioPlayerTest'
import { AudioDebugger } from './components/AudioDebugger'
import { AudioProvider } from './hooks'
import { SimpleAudioProvider } from './hooks/useSimpleAudio'
import { performanceMonitor } from './utils/performanceMonitor'
import { initBrowserCompatibility } from './utils/browserCompatibility'
import { autoRunTests } from './utils/crossBrowserTest'
import { useEffect } from 'react'

function App() {
  // 初始化性能监控和浏览器兼容性检测
  useEffect(() => {
    performanceMonitor.markStart('app-init');

    // 初始化浏览器兼容性检测
    initBrowserCompatibility();

    // 运行跨浏览器测试
    autoRunTests();

    return () => {
      performanceMonitor.markEnd('app-init');

      // 在开发环境中输出性能报告
      if (process.env.NODE_ENV === 'development') {
        setTimeout(() => {
          console.log(performanceMonitor.generateReport());
        }, 2000);
      }
    };
  }, []);

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <AudioProvider>
          <SimpleAudioProvider>
            <ErrorBoundary>
              <CourseShowcasePage />
            </ErrorBoundary>
            {/* 启用音频播放器进行测试 */}
            <AudioPlayerLazy />
            {/* 开发环境下显示调试工具 */}
            {process.env.NODE_ENV === 'development' && (
              <>
                <AudioDebugger />
                <SimpleAudioTest />
                <SimpleAudioPlayer />
                <DirectAudioTest />
                <SimpleAudioPlayerTest />
              </>
            )}
          </SimpleAudioProvider>
        </AudioProvider>
      </ThemeProvider>
    </ErrorBoundary>
  )
}

export default App
