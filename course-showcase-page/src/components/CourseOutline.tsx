import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { courseModules, getAllLessons } from '../data';
import { useAudio, useAccessibility } from '../hooks';
import type { CourseModule, Lesson } from '../types';

// Styled Components
const OutlineContainer = styled.section`
  max-width: 1200px;
  margin: 0 auto;
  /* Mobile-first responsive padding */
  padding: ${({ theme }) => theme.spacing[6]} ${({ theme }) => theme.spacing[4]};

  ${({ theme }) => theme.mediaQueries.tablet} {
    padding: ${({ theme }) => theme.spacing[10]} ${({ theme }) => theme.spacing[6]};
  }

  ${({ theme }) => theme.mediaQueries.desktop} {
    padding: ${({ theme }) => theme.spacing[16]} ${({ theme }) => theme.spacing[8]};
  }
`;

const OutlineHeader = styled.div`
  text-align: center;
  /* Mobile-first responsive margin */
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    margin-bottom: ${({ theme }) => theme.spacing[8]};
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    margin-bottom: ${({ theme }) => theme.spacing[10]};
  }
`;

const OutlineTitle = styled.h2`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  /* Mobile-first responsive font sizing */
  font-size: ${({ theme }) => theme.typography.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.typography.fontWeights.bold};
  color: ${({ theme }) => theme.colors.trustBlue};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
  line-height: ${({ theme }) => theme.typography.lineHeights.tight};

  ${({ theme }) => theme.mediaQueries.tablet} {
    font-size: ${({ theme }) => theme.typography.fontSizes['3xl']};
    margin-bottom: ${({ theme }) => theme.spacing[4]};
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    font-size: ${({ theme }) => theme.typography.fontSizes['4xl']};
  }
`;

const OutlineSubtitle = styled.p`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  color: ${({ theme }) => theme.colors.gray[600]};
  line-height: ${({ theme }) => theme.typography.lineHeights.relaxed};
  max-width: 600px;
  margin: 0 auto ${({ theme }) => theme.spacing[4]} auto;
`;

const KeyboardHints = styled.div`
  /* Hide on mobile to save space */
  display: none;
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: ${({ theme }) => theme.spacing[2]};
    margin-top: ${({ theme }) => theme.spacing[4]};
    font-size: ${({ theme }) => theme.typography.fontSizes.sm};
    color: ${({ theme }) => theme.colors.gray[500]};
  }
`;

const KeyboardHint = styled.span`
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.gray[100]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  font-family: ${({ theme }) => theme.typography.fonts.english};
  
  kbd {
    background: ${({ theme }) => theme.colors.white};
    border: 1px solid ${({ theme }) => theme.colors.gray[300]};
    border-radius: ${({ theme }) => theme.borderRadius.sm};
    padding: 2px 4px;
    font-size: ${({ theme }) => theme.typography.fontSizes.xs};
    font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  }
`;

const ModulesContainer = styled.div`
  display: flex;
  flex-direction: column;
  /* Mobile-first responsive gap */
  gap: ${({ theme }) => theme.spacing[3]};
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    gap: ${({ theme }) => theme.spacing[4]};
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    gap: ${({ theme }) => theme.spacing[5]};
  }
`;

const ModuleCard = styled.div<{ $color: 'growth' | 'trust' | 'action' }>`
  background: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  box-shadow: ${({ theme }) => theme.shadows.base};
  overflow: hidden;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};

  &:hover {
    box-shadow: ${({ theme }) => theme.shadows.lg};
    transform: translateY(-2px);
  }

  ${({ $color, theme }) => {
    const colorMap = {
      growth: theme.colors.growthGreen,
      trust: theme.colors.trustBlue,
      action: theme.colors.actionOrange,
    };
    return `border-left: 4px solid ${colorMap[$color]};`;
  }}
`;

const ModuleHeader = styled.button<{ $isExpanded: boolean; $color: 'growth' | 'trust' | 'action' }>`
  width: 100%;
  /* Mobile-first responsive padding */
  padding: ${({ theme }) => theme.spacing[4]};
  background: transparent;
  border: none;
  cursor: pointer;
  text-align: left;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  /* Ensure minimum touch target */
  min-height: ${({ theme }) => theme.touchTargets.minimum};

  &:hover {
    ${({ $color, theme }) => {
      const colorMap = {
        growth: theme.colors.growthGreen,
        trust: theme.colors.trustBlue,
        action: theme.colors.actionOrange,
      };
      return `background: ${colorMap[$color]}10;`;
    }}
  }

  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: -2px;
  }

  ${({ theme }) => theme.mediaQueries.tablet} {
    padding: ${({ theme }) => theme.spacing[6]};
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    padding: ${({ theme }) => theme.spacing[8]};
  }
`;

const ModuleHeaderContent = styled.div`
  flex: 1;
`;

const ModuleTitle = styled.h3<{ $color: 'growth' | 'trust' | 'action' }>`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  /* Mobile-first responsive font sizing */
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
  line-height: ${({ theme }) => theme.typography.lineHeights.tight};

  ${({ $color, theme }) => {
    const colorMap = {
      growth: theme.colors.growthGreen,
      trust: theme.colors.trustBlue,
      action: theme.colors.actionOrange,
    };
    return `color: ${colorMap[$color]};`;
  }}

  ${({ theme }) => theme.mediaQueries.tablet} {
    font-size: ${({ theme }) => theme.typography.fontSizes.xl};
    margin-bottom: ${({ theme }) => theme.spacing[2]};
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    font-size: ${({ theme }) => theme.typography.fontSizes['2xl']};
  }
`;

const ModuleDescription = styled.p`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-size: ${({ theme }) => theme.typography.fontSizes.base};
  color: ${({ theme }) => theme.colors.gray[600]};
  line-height: ${({ theme }) => theme.typography.lineHeights.normal};
  margin: 0;
`;

const ExpandIcon = styled.div<{ $isExpanded: boolean; $color: 'growth' | 'trust' | 'action' }>`
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  transform: ${({ $isExpanded }) => $isExpanded ? 'rotate(180deg)' : 'rotate(0deg)'};

  ${({ $color, theme }) => {
    const colorMap = {
      growth: theme.colors.growthGreen,
      trust: theme.colors.trustBlue,
      action: theme.colors.actionOrange,
    };
    return `
      background: ${colorMap[$color]}20;
      color: ${colorMap[$color]};
    `;
  }}

  &::after {
    content: '▼';
    font-size: 12px;
    font-weight: bold;
  }
`;

const LessonsContainer = styled.div<{ $isExpanded: boolean }>`
  max-height: ${({ $isExpanded }) => $isExpanded ? '1000px' : '0'};
  overflow: hidden;
  transition: max-height ${({ theme }) => theme.animations.duration.slow} ${({ theme }) => theme.animations.easing.easeInOut};
`;

const LessonsList = styled.div`
  /* Mobile-first responsive padding */
  padding: 0 ${({ theme }) => theme.spacing[4]} ${({ theme }) => theme.spacing[4]};
  border-top: 1px solid ${({ theme }) => theme.colors.gray[200]};

  ${({ theme }) => theme.mediaQueries.tablet} {
    padding: 0 ${({ theme }) => theme.spacing[6]} ${({ theme }) => theme.spacing[6]};
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    padding: 0 ${({ theme }) => theme.spacing[8]} ${({ theme }) => theme.spacing[8]};
  }
`;

const LessonItem = styled.button<{ 
  $isActive: boolean; 
  $isCompleted: boolean;
  $hasError: boolean;
  $color: 'growth' | 'trust' | 'action';
}>`
  width: 100%;
  /* Mobile-first responsive padding */
  padding: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  background: transparent;
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  cursor: pointer;
  text-align: left;
  display: flex;
  align-items: flex-start;
  gap: ${({ theme }) => theme.spacing[3]};
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  min-height: ${({ theme }) => theme.touchTargets.minimum};
  position: relative;

  &:hover {
    ${({ $color, $hasError, theme }) => {
      if ($hasError) {
        return `
          border-color: #ef4444;
          background: #fef2f2;
        `;
      }
      const colorMap = {
        growth: theme.colors.growthGreen,
        trust: theme.colors.trustBlue,
        action: theme.colors.actionOrange,
      };
      return `
        border-color: ${colorMap[$color]};
        background: ${colorMap[$color]}10;
        transform: translateY(-1px);
        box-shadow: ${theme.shadows.md};
      `;
    }}
  }

  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: -2px;
  }

  &:active {
    transform: translateY(0);
  }

  ${({ $isActive, $hasError, theme }) => {
    if ($hasError) {
      return `
        border-color: #ef4444;
        background: #fef2f2;
      `;
    }
    if ($isActive) {
      return `
        border-color: ${theme.colors.actionOrange};
        background: ${theme.colors.actionOrange}20;
        box-shadow: ${theme.shadows.md};
      `;
    }
    return '';
  }}

  ${({ $isCompleted, theme }) => {
    if ($isCompleted) {
      return `
        background: ${theme.colors.growthGreen}10;
        border-color: ${theme.colors.growthGreen};
      `;
    }
    return '';
  }}

  ${({ theme }) => theme.mediaQueries.tablet} {
    padding: ${({ theme }) => theme.spacing[4]};
    min-height: ${({ theme }) => theme.touchTargets.comfortable};
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    padding: ${({ theme }) => theme.spacing[5]};
  }
`;

const PlayIcon = styled.div<{ 
  $isActive: boolean; 
  $isPlaying: boolean;
  $isLoading: boolean;
  $hasError: boolean;
}>`
  /* Mobile-first: larger touch targets */
  width: 36px;
  height: 36px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  position: relative;
  
  /* Smaller on desktop */
  ${({ theme }) => theme.mediaQueries.desktop} {
    width: 32px;
    height: 32px;
  }

  ${({ $isActive, $isPlaying, $isLoading, $hasError, theme }) => {
    if ($hasError) {
      return `
        background: #ef4444;
        color: ${theme.colors.white};
        
        &::after {
          content: '⚠';
          font-size: 14px;
        }
      `;
    }
    
    if ($isLoading) {
      return `
        background: ${theme.colors.actionOrange}40;
        color: ${theme.colors.actionOrange};
        
        &::after {
          content: '';
          position: absolute;
          width: 16px;
          height: 16px;
          border: 2px solid ${theme.colors.actionOrange};
          border-top: 2px solid transparent;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `;
    }
    
    if ($isActive && $isPlaying) {
      return `
        background: ${theme.colors.actionOrange};
        color: ${theme.colors.white};
        box-shadow: 0 0 0 2px ${theme.colors.actionOrange}40;
        
        &::after {
          content: '⏸';
          font-size: 14px;
        }
      `;
    }
    
    if ($isActive) {
      return `
        background: ${theme.colors.actionOrange};
        color: ${theme.colors.white};
        box-shadow: 0 0 0 2px ${theme.colors.actionOrange}40;
        
        &::after {
          content: '▶';
          font-size: 12px;
          margin-left: 2px;
        }
      `;
    }
    
    return `
      background: ${theme.colors.gray[100]};
      color: ${theme.colors.gray[600]};
      
      &:hover {
        background: ${theme.colors.actionOrange}20;
        color: ${theme.colors.actionOrange};
      }
      
      &::after {
        content: '▶';
        font-size: 12px;
        margin-left: 2px;
      }
    `;
  }}
`;

const LessonContent = styled.div`
  flex: 1;
  min-width: 0;
`;

const LessonTitle = styled.h4<{ $isActive: boolean }>`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-size: ${({ theme }) => theme.typography.fontSizes.base};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  margin: 0 0 ${({ theme }) => theme.spacing[1]} 0;
  line-height: ${({ theme }) => theme.typography.lineHeights.tight};
  color: ${({ $isActive, theme }) => 
    $isActive ? theme.colors.actionOrange : theme.colors.gray[800]
  };

  ${({ theme }) => theme.mediaQueries.tablet} {
    font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  }
`;

const LessonMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[500]};
  flex-wrap: wrap;
`;

const CompletionBadge = styled.span`
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.growthGreen}20;
  color: ${({ theme }) => theme.colors.growthGreen};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
`;

const StatusBadge = styled.span<{ $status: 'playing' | 'paused' | 'error' }>`
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};

  ${({ $status, theme }) => {
    switch ($status) {
      case 'playing':
        return `
          background: ${theme.colors.actionOrange}20;
          color: ${theme.colors.actionOrange};
        `;
      case 'paused':
        return `
          background: ${theme.colors.gray[200]};
          color: ${theme.colors.gray[600]};
        `;
      case 'error':
        return `
          background: #fef2f2;
          color: #ef4444;
        `;
      default:
        return '';
    }
  }}
`;

const LessonDuration = styled.span`
  font-family: ${({ theme }) => theme.typography.fonts.english};
`;

const LessonDescription = styled.p`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
  margin: ${({ theme }) => theme.spacing[1]} 0 0 0;
  line-height: ${({ theme }) => theme.typography.lineHeights.normal};
`;

const ProgressBar = styled.div<{ $isActive: boolean }>`
  width: 100%;
  height: 3px;
  background: ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  margin-top: ${({ theme }) => theme.spacing[2]};
  overflow: hidden;
  opacity: ${({ $isActive }) => $isActive ? 1 : 0};
  transition: opacity ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
`;

const ProgressFill = styled.div<{ $progress: number }>`
  height: 100%;
  background: ${({ theme }) => theme.colors.actionOrange};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  width: ${({ $progress }) => $progress}%;
  transition: width ${({ theme }) => theme.animations.duration.fast} ${({ theme }) => theme.animations.easing.easeOut};
`;

// Helper function to format duration
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// CourseOutline Component
export const CourseOutline: React.FC = () => {
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());
  const [focusedLessonId, setFocusedLessonId] = useState<string | null>(null);
  const { state, playLesson, pauseAudio, resumeAudio, setPlaylist, nextLesson, previousLesson } = useAudio();
  const { announceChange } = useAccessibility();
  const outlineRef = useRef<HTMLElement>(null);

  // Initialize playlist with all lessons (only once on mount)
  useEffect(() => {
    const allLessons = getAllLessons();
    setPlaylist(allLessons);
  }, []); // 移除 setPlaylist 依赖，只在组件挂载时运行一次

  // Auto-expand module when a lesson starts playing
  useEffect(() => {
    if (state.currentLesson) {
      const module = courseModules.find(m => 
        m.lessons.some(l => l.id === state.currentLesson?.id)
      );
      if (module) {
        setExpandedModules(prev => new Set([...prev, module.id]));
        
        // Announce module expansion for screen readers
        announceChange(`展开模块: ${module.title}`, 'polite');
        
        // Scroll to active lesson after a short delay to allow expansion animation
        setTimeout(() => {
          const activeElement = document.querySelector(`[data-lesson-id="${state.currentLesson?.id}"]`);
          if (activeElement) {
            activeElement.scrollIntoView({ 
              behavior: 'smooth', 
              block: 'center' 
            });
            announceChange(`滚动到当前播放课程: ${state.currentLesson?.title}`, 'polite');
          }
        }, 300);
      }
    }
  }, [state.currentLesson, announceChange]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle keyboard events when focused on a lesson
      if (!focusedLessonId) return;

      switch (event.key) {
        case ' ':
        case 'Enter':
          event.preventDefault();
          const lesson = getAllLessons().find(l => l.id === focusedLessonId);
          if (lesson) {
            handleLessonClick(lesson);
          }
          break;
        case 'ArrowRight':
        case 'n':
          event.preventDefault();
          if (state.currentLesson) {
            nextLesson();
          }
          break;
        case 'ArrowLeft':
        case 'p':
          event.preventDefault();
          if (state.currentLesson) {
            previousLesson();
          }
          break;
        case 'Escape':
          event.preventDefault();
          setFocusedLessonId(null);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [focusedLessonId, state.currentLesson, nextLesson, previousLesson]);

  const toggleModule = (moduleId: string) => {
    const module = courseModules.find(m => m.id === moduleId);
    if (!module) return;

    setExpandedModules(prev => {
      const newSet = new Set(prev);
      // const isExpanding = !newSet.has(moduleId);
      
      if (newSet.has(moduleId)) {
        newSet.delete(moduleId);
        announceChange(`收起模块: ${module.title}`, 'polite');
      } else {
        newSet.add(moduleId);
        announceChange(`展开模块: ${module.title}, 包含${module.lessons.length}个课程`, 'polite');
      }
      return newSet;
    });
  };

  const handleLessonClick = (lesson: Lesson) => {
    console.log('=== handleLessonClick START ===');
    console.log('Clicked lesson:', lesson.title, lesson.audioUrl);
    console.log('Current state:', {
      currentLesson: state.currentLesson?.title,
      isPlaying: state.isPlaying,
      isLoading: state.isLoading
    });
    const isCurrentLesson = state.currentLesson?.id === lesson.id;
    console.log('Is current lesson?', isCurrentLesson);
    
    if (isCurrentLesson) {
      // Toggle play/pause for current lesson
      console.log('Toggling current lesson, isPlaying:', state.isPlaying);
      if (state.isPlaying) {
        pauseAudio();
        announceChange(`暂停播放: ${lesson.title}`, 'polite');
      } else {
        resumeAudio();
        announceChange(`继续播放: ${lesson.title}`, 'polite');
      }
    } else {
      // Play new lesson
      console.log('Playing new lesson:', lesson.title);
      console.log('Calling playLesson...');
      playLesson(lesson);
      console.log('playLesson called, announcing change...');
      announceChange(`开始播放: ${lesson.title}`, 'polite');

      // Auto-expand the module containing this lesson
      const module = courseModules.find(m => m.lessons.some(l => l.id === lesson.id));
      if (module && !expandedModules.has(module.id)) {
        setExpandedModules(prev => new Set([...prev, module.id]));
      }
    }
    console.log('=== handleLessonClick END ===');
  };

  const handleLessonFocus = (lessonId: string) => {
    setFocusedLessonId(lessonId);
  };

  const handleLessonBlur = () => {
    setFocusedLessonId(null);
  };

  const isLessonActive = (lesson: Lesson): boolean => {
    return state.currentLesson?.id === lesson.id;
  };

  const isLessonLoading = (lesson: Lesson): boolean => {
    return isLessonActive(lesson) && state.isLoading;
  };

  const getLessonProgress = (lesson: Lesson): number => {
    if (!isLessonActive(lesson) || state.duration === 0) return 0;
    return (state.currentTime / state.duration) * 100;
  };

  return (
    <OutlineContainer 
      ref={outlineRef}
      id="outline"
      aria-labelledby="outline-heading"
      role="region"
      aria-describedby="outline-description"
    >
      <OutlineHeader>
        <OutlineTitle id="outline-heading">课程大纲</OutlineTitle>
        <OutlineSubtitle id="outline-description">
          5大核心模块，从理论基础到实战应用，系统构建你的咨询能力
        </OutlineSubtitle>
        <KeyboardHints aria-label="键盘快捷键提示">
          <KeyboardHint>
            <kbd>Space</kbd> 播放/暂停
          </KeyboardHint>
          <KeyboardHint>
            <kbd>←</kbd> 上一课
          </KeyboardHint>
          <KeyboardHint>
            <kbd>→</kbd> 下一课
          </KeyboardHint>
          <KeyboardHint>
            <kbd>Esc</kbd> 取消焦点
          </KeyboardHint>
        </KeyboardHints>
      </OutlineHeader>

      <ModulesContainer role="list" aria-label="课程模块列表">
        {courseModules.map((module: CourseModule) => (
          <ModuleCard key={module.id} $color={module.color} role="listitem">
            <ModuleHeader
              $isExpanded={expandedModules.has(module.id)}
              $color={module.color}
              onClick={() => toggleModule(module.id)}
              aria-expanded={expandedModules.has(module.id)}
              aria-controls={`module-${module.id}-content`}
              aria-label={`${module.title} - ${expandedModules.has(module.id) ? '收起' : '展开'}课程列表`}
            >
              <ModuleHeaderContent>
                <ModuleTitle $color={module.color}>
                  {module.title}
                </ModuleTitle>
                <ModuleDescription>
                  {module.description}
                </ModuleDescription>
              </ModuleHeaderContent>
              <ExpandIcon 
                $isExpanded={expandedModules.has(module.id)} 
                $color={module.color}
                aria-hidden="true"
              />
            </ModuleHeader>

            <LessonsContainer 
              $isExpanded={expandedModules.has(module.id)}
              id={`module-${module.id}-content`}
            >
              <LessonsList role="list" aria-label={`${module.title}课程列表`}>
                {module.lessons.map((lesson: Lesson) => (
                  <LessonItem
                    key={lesson.id}
                    data-lesson-id={lesson.id}
                    $isActive={isLessonActive(lesson)}
                    $isCompleted={lesson.isCompleted}
                    $hasError={isLessonActive(lesson) && !!state.error}
                    $color={module.color}
                    onClick={() => handleLessonClick(lesson)}
                    onFocus={() => handleLessonFocus(lesson.id)}
                    onBlur={handleLessonBlur}
                    aria-label={`
                      ${lesson.title}. 
                      时长: ${formatDuration(lesson.duration)}. 
                      ${lesson.isCompleted ? '已完成' : '未完成'}. 
                      ${isLessonActive(lesson) ? 
                        (state.isPlaying ? '正在播放' : '已暂停') : 
                        '点击播放'
                      }
                    `}
                    aria-pressed={isLessonActive(lesson) && state.isPlaying}
                    role="button"
                    tabIndex={0}
                  >
                    <PlayIcon
                      $isActive={isLessonActive(lesson)}
                      $isPlaying={isLessonActive(lesson) && state.isPlaying}
                      $isLoading={isLessonLoading(lesson)}
                      $hasError={isLessonActive(lesson) && !!state.error}
                      aria-hidden="true"
                    />
                    <LessonContent>
                      <LessonTitle $isActive={isLessonActive(lesson)}>
                        {lesson.title}
                      </LessonTitle>
                      <LessonMeta>
                        <LessonDuration>
                          {formatDuration(lesson.duration)}
                        </LessonDuration>
                        {lesson.isCompleted && (
                          <CompletionBadge>
                            ✓ 已完成
                          </CompletionBadge>
                        )}
                        {isLessonActive(lesson) && state.error && (
                          <StatusBadge $status="error">
                            ⚠ 播放错误
                          </StatusBadge>
                        )}
                        {isLessonActive(lesson) && !state.error && state.isPlaying && (
                          <StatusBadge $status="playing">
                            ▶ 正在播放
                          </StatusBadge>
                        )}
                        {isLessonActive(lesson) && !state.error && !state.isPlaying && !state.isLoading && (
                          <StatusBadge $status="paused">
                            ⏸ 已暂停
                          </StatusBadge>
                        )}
                      </LessonMeta>
                      {lesson.description && (
                        <LessonDescription>
                          {lesson.description}
                        </LessonDescription>
                      )}
                      <ProgressBar $isActive={isLessonActive(lesson)}>
                        <ProgressFill $progress={getLessonProgress(lesson)} />
                      </ProgressBar>
                    </LessonContent>
                  </LessonItem>
                ))}
              </LessonsList>
            </LessonsContainer>
          </ModuleCard>
        ))}
      </ModulesContainer>
    </OutlineContainer>
  );
};