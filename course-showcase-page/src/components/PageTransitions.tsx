import React, { useEffect, useState } from 'react';
import styled, { keyframes, css } from 'styled-components';

// 页面加载动画
const fadeInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const slideInLeft = keyframes`
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const slideInRight = keyframes`
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const scaleIn = keyframes`
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
`;

// 微交互动画
const bounce = keyframes`
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

// 动画容器组件
interface AnimatedContainerProps {
  animation?: 'fadeInUp' | 'slideInLeft' | 'slideInRight' | 'scaleIn';
  delay?: number;
  duration?: number;
  children: React.ReactNode;
  className?: string;
}

const getAnimation = (animation: string) => {
  switch (animation) {
    case 'fadeInUp':
      return fadeInUp;
    case 'slideInLeft':
      return slideInLeft;
    case 'slideInRight':
      return slideInRight;
    case 'scaleIn':
      return scaleIn;
    default:
      return fadeInUp;
  }
};

const AnimatedWrapper = styled.div<{
  animation: string;
  delay: number;
  duration: number;
}>`
  animation: ${({ animation }) => getAnimation(animation)} 
             ${({ duration }) => duration}s 
             ${({ delay }) => delay}s 
             ease-out both;
`;

export const AnimatedContainer: React.FC<AnimatedContainerProps> = ({
  animation = 'fadeInUp',
  delay = 0,
  duration = 0.6,
  children,
  className
}) => {
  return (
    <AnimatedWrapper
      animation={animation}
      delay={delay}
      duration={duration}
      className={className}
    >
      {children}
    </AnimatedWrapper>
  );
};

// 交互式按钮动画
interface InteractiveButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'bounce' | 'pulse' | 'lift';
  className?: string;
  disabled?: boolean;
}

const InteractiveButtonWrapper = styled.button<{
  variant: string;
  disabled: boolean;
}>`
  border: none;
  background: none;
  cursor: ${({ disabled }) => disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;
  opacity: ${({ disabled }) => disabled ? 0.6 : 1};
  
  ${({ variant, disabled }) => !disabled && css`
    &:hover {
      ${variant === 'bounce' && css`
        animation: ${bounce} 0.6s ease;
      `}
      
      ${variant === 'pulse' && css`
        animation: ${pulse} 0.6s ease;
      `}
      
      ${variant === 'lift' && css`
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      `}
    }
    
    &:active {
      transform: ${variant === 'lift' ? 'translateY(0)' : 'scale(0.98)'};
    }
  `}
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
  }
`;

export const InteractiveButton: React.FC<InteractiveButtonProps> = ({
  children,
  onClick,
  variant = 'lift',
  className,
  disabled = false
}) => {
  return (
    <InteractiveButtonWrapper
      variant={variant}
      onClick={onClick}
      className={className}
      disabled={disabled}
      aria-disabled={disabled}
    >
      {children}
    </InteractiveButtonWrapper>
  );
};

// 加载动画组件
const ShimmerWrapper = styled.div`
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.colors.background} 0%,
    ${({ theme }) => theme.colors.warmSand} 50%,
    ${({ theme }) => theme.colors.background} 100%
  );
  background-size: 200px 100%;
  animation: ${shimmer} 1.5s infinite;
  border-radius: ${({ theme }) => theme.borderRadius.md};
`;

interface ShimmerLoadingProps {
  width?: string;
  height?: string;
  className?: string;
}

export const ShimmerLoading: React.FC<ShimmerLoadingProps> = ({
  width = '100%',
  height = '20px',
  className
}) => {
  return (
    <ShimmerWrapper
      className={className}
      style={{ width, height }}
      aria-label="内容加载中"
    />
  );
};

// 滚动触发动画Hook
export const useScrollAnimation = (threshold = 0.1) => {
  const [isVisible, setIsVisible] = useState(false);
  const [elementRef, setElementRef] = useState<HTMLElement | null>(null);

  useEffect(() => {
    if (!elementRef) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(elementRef);
        }
      },
      { threshold }
    );

    observer.observe(elementRef);

    return () => {
      if (elementRef) {
        observer.unobserve(elementRef);
      }
    };
  }, [elementRef, threshold]);

  return { isVisible, setElementRef };
};

// 滚动动画容器
interface ScrollAnimationContainerProps {
  children: React.ReactNode;
  animation?: 'fadeInUp' | 'slideInLeft' | 'slideInRight' | 'scaleIn';
  threshold?: number;
  className?: string;
}

export const ScrollAnimationContainer: React.FC<ScrollAnimationContainerProps> = ({
  children,
  animation = 'fadeInUp',
  threshold = 0.1,
  className
}) => {
  const { isVisible, setElementRef } = useScrollAnimation(threshold);

  return (
    <div
      ref={setElementRef}
      className={className}
      style={{
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? 'translateY(0)' : 'translateY(30px)',
        transition: 'opacity 0.6s ease, transform 0.6s ease'
      }}
    >
      {children}
    </div>
  );
};

// 页面加载进度条
const ProgressBarContainer = styled.div<{ isVisible: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: ${({ theme }) => theme.colors.background};
  z-index: 9999;
  opacity: ${({ isVisible }) => isVisible ? 1 : 0};
  transition: opacity 0.3s ease;
`;

const ProgressBar = styled.div<{ progress: number }>`
  height: 100%;
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.colors.trustBlue} 0%,
    ${({ theme }) => theme.colors.actionOrange} 100%
  );
  width: ${({ progress }) => progress}%;
  transition: width 0.3s ease;
`;

interface PageLoadingProgressProps {
  isLoading: boolean;
  progress: number;
}

export const PageLoadingProgress: React.FC<PageLoadingProgressProps> = ({
  isLoading,
  progress
}) => {
  return (
    <ProgressBarContainer isVisible={isLoading}>
      <ProgressBar progress={progress} />
    </ProgressBarContainer>
  );
};

// 平滑滚动工具函数
export const smoothScrollTo = (elementId: string, offset = 0) => {
  const element = document.getElementById(elementId);
  if (element) {
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - offset;

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  }
};

// 页面过渡管理器
export const usePageTransition = () => {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);

  const startTransition = () => {
    setIsTransitioning(true);
    setLoadingProgress(0);
    
    // 模拟加载进度
    const interval = setInterval(() => {
      setLoadingProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(() => setIsTransitioning(false), 300);
          return 100;
        }
        return prev + Math.random() * 30;
      });
    }, 100);
  };

  return {
    isTransitioning,
    loadingProgress,
    startTransition
  };
};

export default {
  AnimatedContainer,
  InteractiveButton,
  ShimmerLoading,
  ScrollAnimationContainer,
  PageLoadingProgress,
  useScrollAnimation,
  usePageTransition,
  smoothScrollTo
};