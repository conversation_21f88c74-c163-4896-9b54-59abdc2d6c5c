import React, { useMemo } from 'react';
import styled from 'styled-components';
import { useAudio } from '../hooks';

const DebugContainer = styled.div`
  position: fixed;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 15px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 12px;
  z-index: 10000;
  max-width: 400px;
  line-height: 1.4;
`;

const DebugTitle = styled.h4`
  margin: 0 0 10px 0;
  color: #00ff00;
`;

const DebugItem = styled.div`
  margin: 5px 0;
  
  strong {
    color: #ffff00;
  }
`;

const StatusIndicator = styled.span<{ $status: 'good' | 'warning' | 'error' }>`
  color: ${({ $status }) => {
    switch ($status) {
      case 'good': return '#00ff00';
      case 'warning': return '#ffaa00';
      case 'error': return '#ff0000';
      default: return '#ffffff';
    }
  }};
`;

export const AudioDebugger: React.FC = () => {
  const { state, audioRef } = useAudio();

  // 直接访问音频元素，不使用 useMemo 避免依赖问题
  const audio = audioRef.current;

  const getStatusColor = (condition: boolean): 'good' | 'error' => {
    return condition ? 'good' : 'error';
  };

  return (
    <DebugContainer>
      <DebugTitle>🎵 音频调试器</DebugTitle>
      
      <DebugItem>
        <strong>当前课程:</strong> {state.currentLesson?.title || '无'}
      </DebugItem>
      
      <DebugItem>
        <strong>音频URL:</strong> {state.currentLesson?.audioUrl || '无'}
      </DebugItem>
      
      <DebugItem>
        <strong>播放状态:</strong>
        <StatusIndicator $status={getStatusColor(state.isPlaying)}>
          {state.isPlaying ? '播放中' : '已暂停'}
        </StatusIndicator>
      </DebugItem>

      <DebugItem>
        <strong>应该播放:</strong>
        <StatusIndicator $status={getStatusColor((state as any).shouldPlay)}>
          {(state as any).shouldPlay ? '是' : '否'}
        </StatusIndicator>
      </DebugItem>
      
      <DebugItem>
        <strong>加载状态:</strong> 
        <StatusIndicator $status={state.isLoading ? 'warning' : 'good'}>
          {state.isLoading ? '加载中' : '已加载'}
        </StatusIndicator>
      </DebugItem>
      
      <DebugItem>
        <strong>错误信息:</strong> 
        <StatusIndicator $status={state.error ? 'error' : 'good'}>
          {state.error || '无错误'}
        </StatusIndicator>
      </DebugItem>
      
      <DebugItem>
        <strong>音频元素:</strong> 
        <StatusIndicator $status={getStatusColor(!!audio)}>
          {audio ? '已创建' : '未创建'}
        </StatusIndicator>
      </DebugItem>
      
      {audio && (
        <>
          <DebugItem>
            <strong>音频源:</strong> {audio.src || '无'}
          </DebugItem>
          
          <DebugItem>
            <strong>音频时长:</strong> {audio.duration ? `${Math.round(audio.duration)}秒` : '未知'}
          </DebugItem>
          
          <DebugItem>
            <strong>当前时间:</strong> {Math.round(audio.currentTime)}秒
          </DebugItem>
          
          <DebugItem>
            <strong>音频状态:</strong> 
            <StatusIndicator $status={audio.paused ? 'warning' : 'good'}>
              {audio.paused ? '暂停' : '播放'}
            </StatusIndicator>
          </DebugItem>
          
          <DebugItem>
            <strong>网络状态:</strong> {audio.networkState}
          </DebugItem>
          
          <DebugItem>
            <strong>就绪状态:</strong> {audio.readyState}
          </DebugItem>
        </>
      )}
      
      <DebugItem>
        <strong>播放列表:</strong> {state.playlist.length}个课程
      </DebugItem>
      
      <DebugItem>
        <strong>当前索引:</strong> {state.currentIndex}
      </DebugItem>
    </DebugContainer>
  );
};

export default AudioDebugger;
