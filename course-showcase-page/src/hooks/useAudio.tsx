import { useContext, useReducer, useRef, useEffect, createContext, useCallback } from 'react';
import type { ReactNode } from 'react';
import type { AudioState, Lesson } from '../types';
import { useAudioLoader } from './useAudioLoader';
import { monitorAudioPerformance } from '../utils/performanceMonitor';

// Audio Actions
export type AudioAction =
  | { type: 'PLAY'; lesson: Lesson }
  | { type: 'START_PLAYING' } // 新增：音频真正开始播放时
  | { type: 'PAUSE' }
  | { type: 'RESUME' }
  | { type: 'STOP' }
  | { type: 'SEEK'; time: number }
  | { type: 'SET_VOLUME'; volume: number }
  | { type: 'SET_DURATION'; duration: number }
  | { type: 'UPDATE_TIME'; currentTime: number }
  | { type: 'SET_LOADING'; isLoading: boolean }
  | { type: 'SET_PLAYLIST'; playlist: Lesson[] }
  | { type: 'NEXT_LESSON' }
  | { type: 'PREVIOUS_LESSON' }
  | { type: 'AUDIO_ENDED' }
  | { type: 'SET_ERROR'; error: string | null };

// Extended Audio State with error handling
export interface ExtendedAudioState extends AudioState {
  error: string | null;
  shouldPlay: boolean; // 是否应该播放（用户意图）
}

// Initial state
const initialState: ExtendedAudioState = {
  currentLesson: null,
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  volume: 0.8,
  isLoading: false,
  playlist: [],
  currentIndex: -1,
  error: null,
  shouldPlay: false
};

// Audio reducer
export const audioReducer = (state: ExtendedAudioState, action: AudioAction): ExtendedAudioState => {
  switch (action.type) {
    case 'PLAY':
      const newIndex = state.playlist.findIndex(lesson => lesson.id === action.lesson.id);
      return {
        ...state,
        currentLesson: action.lesson,
        currentIndex: newIndex,
        isPlaying: false, // 先设置为false，等音频加载完成后再设置为true
        shouldPlay: true, // 用户想要播放
        isLoading: true,
        error: null,
        currentTime: 0
      };

    case 'START_PLAYING':
      return {
        ...state,
        isPlaying: true,
        isLoading: false,
        shouldPlay: false // 清除播放标志，避免重复触发
      };

    case 'PAUSE':
      return {
        ...state,
        isPlaying: false,
        shouldPlay: false
      };

    case 'RESUME':
      return {
        ...state,
        isPlaying: true,
        error: null
      };

    case 'STOP':
      return {
        ...state,
        isPlaying: false,
        currentTime: 0
      };

    case 'SEEK':
      return {
        ...state,
        currentTime: action.time
      };

    case 'SET_VOLUME':
      return {
        ...state,
        volume: Math.max(0, Math.min(1, action.volume))
      };

    case 'SET_DURATION':
      return {
        ...state,
        duration: action.duration,
        isLoading: false
      };

    case 'UPDATE_TIME':
      return {
        ...state,
        currentTime: action.currentTime
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.isLoading
      };

    case 'SET_PLAYLIST':
      return {
        ...state,
        playlist: action.playlist
      };

    case 'NEXT_LESSON':
      if (state.currentIndex < state.playlist.length - 1) {
        const nextLesson = state.playlist[state.currentIndex + 1];
        return {
          ...state,
          currentLesson: nextLesson,
          currentIndex: state.currentIndex + 1,
          isPlaying: true,
          isLoading: true,
          currentTime: 0,
          error: null
        };
      }
      return state;

    case 'PREVIOUS_LESSON':
      if (state.currentIndex > 0) {
        const prevLesson = state.playlist[state.currentIndex - 1];
        return {
          ...state,
          currentLesson: prevLesson,
          currentIndex: state.currentIndex - 1,
          isPlaying: true,
          isLoading: true,
          currentTime: 0,
          error: null
        };
      }
      return state;

    case 'AUDIO_ENDED':
      // Auto-play next lesson if available
      if (state.currentIndex < state.playlist.length - 1) {
        const nextLesson = state.playlist[state.currentIndex + 1];
        return {
          ...state,
          currentLesson: nextLesson,
          currentIndex: state.currentIndex + 1,
          isPlaying: false,
          shouldPlay: true,
          isLoading: true,
          currentTime: 0,
          error: null
        };
      } else {
        // End of playlist
        return {
          ...state,
          isPlaying: false,
          shouldPlay: false,
          currentTime: 0
        };
      }

    case 'SET_ERROR':
      return {
        ...state,
        error: action.error,
        isLoading: false,
        isPlaying: false
      };

    default:
      return state;
  }
};

// Audio Context
export interface AudioContextType {
  state: ExtendedAudioState;
  dispatch: React.Dispatch<AudioAction>;
  audioRef: React.RefObject<HTMLAudioElement | null>;
  playLesson: (lesson: Lesson) => void;
  pauseAudio: () => void;
  resumeAudio: () => void;
  stopAudio: () => void;
  seekTo: (time: number) => void;
  setVolume: (volume: number) => void;
  nextLesson: () => void;
  previousLesson: () => void;
  setPlaylist: (playlist: Lesson[]) => void;
}

const AudioContext = createContext<AudioContextType | undefined>(undefined);

// Audio Provider Component
export interface AudioProviderProps {
  children: ReactNode;
}

export const AudioProvider = ({ children }: AudioProviderProps) => {
  const [state, dispatch] = useReducer(audioReducer, initialState);
  const audioRef = useRef<HTMLAudioElement>(null);
  const performanceCleanupRef = useRef<(() => void) | null>(null);
  
  // 集成音频加载器
  const { loadState, preloadAudio } = useAudioLoader({
    maxRetries: 3,
    retryDelay: 1000,
    onLoadStart: () => dispatch({ type: 'SET_LOADING', isLoading: true }),
    onLoadEnd: () => dispatch({ type: 'SET_LOADING', isLoading: false }),
    onError: (error) => dispatch({ type: 'SET_ERROR', error })
  });

  // Audio event handlers
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      console.log('Audio metadata loaded, duration:', audio.duration, 'src:', audio.src);
      dispatch({ type: 'SET_DURATION', duration: audio.duration });
    };

    const handleTimeUpdate = () => {
      dispatch({ type: 'UPDATE_TIME', currentTime: audio.currentTime });
    };

    const handleCanPlay = () => {
      console.log('Audio can play');
      dispatch({ type: 'SET_LOADING', isLoading: false });
    };

    const handleError = (event) => {
      console.error('Audio error:', audio.error, 'src:', audio.src, 'event:', event);
      const errorMessage = audio.error ?
        `音频加载错误 (${audio.error.code}): ${audio.error.message}` :
        '音频加载失败';
      dispatch({ type: 'SET_ERROR', error: errorMessage });
    };

    const handleEnded = () => {
      // 直接 dispatch action，reducer 会处理状态逻辑
      dispatch({ type: 'AUDIO_ENDED' });
    };

    // Add event listeners
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('error', handleError);
    audio.addEventListener('ended', handleEnded);

    // Cleanup
    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('ended', handleEnded);
    };
  }, []); // 移除依赖，避免无限循环

  // Sync audio element with state and performance monitoring
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    // 检查是否需要加载新的音频文件
    const needsNewAudio = state.currentLesson && (
      !audio.src ||
      !audio.src.endsWith(state.currentLesson.audioUrl)
    );

    if (needsNewAudio) {
      console.log('Loading audio:', state.currentLesson.audioUrl);
      console.log('Current audio.src:', audio.src);
      
      // 清理之前的性能监控
      if (performanceCleanupRef.current) {
        performanceCleanupRef.current();
      }
      
      // 直接设置音频源
      audio.src = state.currentLesson.audioUrl;
      audio.load();
      
      // 开始性能监控
      performanceCleanupRef.current = monitorAudioPerformance(
        audio, 
        state.currentLesson.title
      );
    }

    // 处理暂停状态
    if (!state.isPlaying && state.currentLesson) {
      audio.pause();
    }

    audio.volume = state.volume;
  }, [state.currentLesson, state.isPlaying, state.volume]); // 移除 state.shouldPlay 依赖，避免无限循环

  // 专门处理播放意图的 useEffect
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio || !state.shouldPlay || state.isPlaying || !state.currentLesson) return;

    console.log('Handling shouldPlay=true, attempting to play...');

    // 检查音频是否准备好
    if (audio.readyState >= 3) {
      console.log('Audio is ready, playing immediately');
      audio.play().then(() => {
        console.log('Audio started playing successfully');
        dispatch({ type: 'START_PLAYING' });
      }).catch((error) => {
        console.error('Play failed:', error);
        dispatch({ type: 'SET_ERROR', error: `播放失败: ${error.message}` });
      });
    } else {
      console.log('Audio not ready, waiting for canplay event');
      const handleCanPlay = () => {
        console.log('Audio can play, attempting to start playback...');
        audio.play().then(() => {
          console.log('Audio started playing successfully');
          dispatch({ type: 'START_PLAYING' });
        }).catch((error) => {
          console.error('Play failed:', error);
          dispatch({ type: 'SET_ERROR', error: `播放失败: ${error.message}` });
        });
        audio.removeEventListener('canplay', handleCanPlay);
      };
      audio.addEventListener('canplay', handleCanPlay);

      // 清理函数
      return () => {
        audio.removeEventListener('canplay', handleCanPlay);
      };
    }
  }, [state.shouldPlay, state.isPlaying, state.currentLesson]);

  // 预加载下一首音频
  useEffect(() => {
    if (state.currentIndex >= 0 && state.currentIndex < state.playlist.length - 1) {
      const nextLesson = state.playlist[state.currentIndex + 1];
      if (nextLesson) {
        preloadAudio(nextLesson).catch((error) => {
          console.warn('Failed to preload next audio:', error);
        });
      }
    }
  }, [state.currentIndex, state.playlist, preloadAudio]);

  // 清理性能监控
  useEffect(() => {
    return () => {
      if (performanceCleanupRef.current) {
        performanceCleanupRef.current();
      }
    };
  }, []);

  // Action creators
  const playLesson = useCallback((lesson: Lesson) => {
    console.log('playLesson called with:', lesson.title, lesson.audioUrl);
    dispatch({ type: 'PLAY', lesson });
  }, []); // 移除所有依赖，避免无限循环

  const pauseAudio = useCallback(() => {
    dispatch({ type: 'PAUSE' });
  }, []);

  const resumeAudio = useCallback(() => {
    dispatch({ type: 'RESUME' });
  }, []);

  const stopAudio = useCallback(() => {
    dispatch({ type: 'STOP' });
  }, []);

  const seekTo = useCallback((time: number) => {
    const audio = audioRef.current;
    if (audio) {
      audio.currentTime = time;
      dispatch({ type: 'SEEK', time });
    }
  }, []);

  const setVolume = useCallback((volume: number) => {
    dispatch({ type: 'SET_VOLUME', volume });
  }, []);

  const nextLesson = useCallback(() => {
    dispatch({ type: 'NEXT_LESSON' });
  }, []);

  const previousLesson = useCallback(() => {
    dispatch({ type: 'PREVIOUS_LESSON' });
  }, []);

  const setPlaylist = useCallback((playlist: Lesson[]) => {
    dispatch({ type: 'SET_PLAYLIST', playlist });
  }, []);

  const contextValue: AudioContextType = {
    state: { ...state, ...loadState },
    dispatch,
    audioRef,
    playLesson,
    pauseAudio,
    resumeAudio,
    stopAudio,
    seekTo,
    setVolume,
    nextLesson,
    previousLesson,
    setPlaylist
  };

  return (
    <AudioContext.Provider value={contextValue}>
      {children}
      {/* Hidden audio element */}
      <audio
        ref={audioRef}
        preload="metadata"
        style={{ display: 'none' }}
      />
    </AudioContext.Provider>
  );
};

// Custom hook to use audio context
export const useAudio = (): AudioContextType => {
  const context = useContext(AudioContext);
  if (context === undefined) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
};